<?php

  AppModel::loadModelClass('IndufastCalendarEventEmployeeModel');

  class IndufastCalendarEventEmployee extends IndufastCalendarEventEmployeeModel {

    use PropertyCastTrait {
      castProperties as castPropertiesTrait;
    }
    use ModelFillTrait;
    use ModelValidateTrait;

    protected array $fillable = [
      'calendar_event_id' => 'required|integer|exists:indufast_calendar_event,id',
      'employee_id'       => 'required|integer|exists:indufast_employee,id',
    ];

    const CAST_PROPERTIES = [
      'id'                => 'int',
      'calendar_event_id' => 'int',
      'employee_id'       => 'int',
      'conflicts'         => 'array',
      'from_db'           => 'hidden',
    ];

    public IndufastEmployee $employee;
    public IndufastCalendarEvent $calendar_event;

    public function castProperties(): void {
      $this->employee();
      $this->castPropertiesTrait();

      // Parse conflicts JSON
      if (is_string($this->conflicts)) {
        $this->conflicts = json_decode($this->conflicts, true) ?: [];
      }
      if (!is_array($this->conflicts)) {
        $this->conflicts = [];
      }
    }

    public function employee(): IndufastEmployee {
      if ($employee = IndufastEmployee::find_by_id($this->employee_id)) {
        return $this->employee = $employee;
      }

      throw new Exception("Employee not found");
    }

    public function event(): IndufastCalendarEvent {
      if ($calendar_event = IndufastCalendarEvent::find_by_id($this->calendar_event_id)) {
        return $this->calendar_event = $calendar_event;
      }

      throw new Exception("Event not found");
    }

    /**
     * Check if this event employee has any conflicts
     */
    public function hasConflicts(): bool {
      return !empty($this->conflicts);
    }

    /**
     * Get conflicts for a specific employee
     */
    public function getConflictsForEmployee(int $employeeId): array {
      return array_filter($this->conflicts, fn($conflict) => $conflict['employee_id'] === $employeeId);
    }

    /**
     * Add a conflict to this event employee
     */
    public function addConflict(int $employeeId, string $reason): void {
      $this->conflicts[] = [
        'employee_id' => $employeeId,
        'reason' => $reason
      ];
    }

    /**
     * Clear all conflicts
     */
    public function clearConflicts(): void {
      $this->conflicts = [];
    }

  }