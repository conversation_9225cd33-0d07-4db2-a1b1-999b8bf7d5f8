<?php

  namespace domain\project\service;

  use DBConn;
  use IndufastCalendarEvent;
  use IndufastCalendarEventEmployee;
  use IndufastEmployee;
  use IndufastProject;

  class ProjectUpdater {

    /**
     * @throws \GsdDbException
     * @throws \GsdException
     * @throws \Exception
     */
    public static function updateProjectValidity(): void {
      // We only want to check for one month ahead
      $today = date('Y-m-d');
      $oneMonthAhead = date('Y-m-d', strtotime('+1 month'));

      $employees = IndufastEmployee::find_all();
      $activeEmployees = array_filter($employees, fn($e) => $e->active === 1);
      $eventsByEmployee = IndufastEmployee::getCalendarEventsForEmployees($activeEmployees, $today, $oneMonthAhead); // active only
      $indufastEvents = IndufastCalendarEvent::find_all("WHERE start >= '$today 0:00:00' AND start < '$oneMonthAhead 0:00:00' ORDER BY start");

      foreach ($indufastEvents as $event) {
        $event->dayPart();

        $eventEmployees = $event->employees();
        if (empty($eventEmployees)) continue;

        foreach ($eventEmployees as $eventEmployee) {
          $conflicts = [];
          $employee = array_find($employees, fn($e) => $e->id === $eventEmployee->employee_id);

          if (!$employee) {
            // Employee not found (inactive or deleted)
            logToFile(__FUNCTION__, "Employee not found: " . var_export($eventEmployee, 1));
            $conflicts[] = [
              'employee_id' => $eventEmployee->employee_id,
              'reason' => 'employee_not_found'
            ];
          } else {
            // Check if employee is inactive
            if ($employee->active !== 1) {
              $conflicts[] = [
                'employee_id' => $employee->id,
                'reason' => 'employee_inactive'
              ];
            } else {
              // Check availability conflicts for active employees
              $date = date('Y-m-d', strtotime($event->start));
              $eventsForEmployee = $eventsByEmployee[$employee->id] ?? [];
              $availability = $employee->calculateAvailabilityFromEvents($eventsForEmployee, $date, false, $event);

              if ($employee->hasConflict($event, $availability)) {
                $conflicts[] = [
                  'employee_id' => $employee->id,
                  'reason' => self::getConflictReason($event, $availability)
                ];
              }
            }
          }

          // Store conflicts as JSON array
          $eventEmployee->conflicts = json_encode($conflicts);
          $eventEmployee->from_db = true;
          $eventEmployee->save();
        }
      }
    }

    public static function cleanupPastEvents(): void {
      $event = IndufastCalendarEvent::getTablename();
      $eventEmployee = IndufastCalendarEventEmployee::getTablename();

      // update all events from before yesterday
      $yesterday = date('Y-m-d 0:00:00', strtotime('-1 day'));
      $query = <<<SQL
        UPDATE $eventEmployee
        SET conflicts = '[]'
        WHERE calendar_event_id IN (
          SELECT id FROM $event
          WHERE start < '$yesterday'
        )
      SQL;
      DBConn::db_link()->query($query);
    }

    /**
     * Get the specific reason for a conflict based on event and availability
     */
    private static function getConflictReason(IndufastCalendarEvent $event, string $availability): string {
      return match ($availability) {
        IndufastEmployee::AVAILABLE_MORNING => $event->day_part !== \IndufastCalendarEvent::DAY_PART_MORNING ? 'schedule_conflict_not_morning' : 'unknown_conflict',
        IndufastEmployee::AVAILABLE_AFTERNOON => $event->day_part !== \IndufastCalendarEvent::DAY_PART_AFTERNOON ? 'schedule_conflict_not_afternoon' : 'unknown_conflict',
        IndufastEmployee::AVAILABLE_MORNING_AFTERNOON => !in_array($event->day_part, [\IndufastCalendarEvent::DAY_PART_MORNING, \IndufastCalendarEvent::DAY_PART_AFTERNOON]) ? 'schedule_conflict_not_morning_afternoon' : 'unknown_conflict',
        IndufastEmployee::NOT_AVAILABLE => 'not_available',
        IndufastEmployee::NOT_AVAILABLE_ERROR => 'availability_error',
        default => 'schedule_conflict',
      };
    }
  }